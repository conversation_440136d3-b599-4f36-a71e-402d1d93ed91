/**
 * TCP Client per testare la nuova logica di gestione viaggi
 */

import net from 'net';

// Messaggi di test nello scenario richiesto
const testMessages = [
  // Messaggio 1: oggi 10:00 - diventa "Start" (primo messaggio TCP)
  '123456789012345,2024-01-15T10:00:00.000Z,45.4642,9.1900,0,0,85,Fixed',
  
  // Messaggio 2: ieri 22:00 - dovrebbe creare viaggio retroattivo
  '123456789012345,2024-01-14T22:00:00.000Z,45.4500,9.1800,45,90,90,Fixed',
  
  // Messaggio 3: ieri 22:01 - dovrebbe andare nel viaggio retroattivo
  '123456789012345,2024-01-14T22:01:00.000Z,45.4510,9.1810,50,95,89,Fixed',
  
  // Messaggio 4: oggi 10:01 - dovrebbe andare nel viaggio principale
  '123456789012345,2024-01-15T10:01:00.000Z,45.4650,9.1910,30,45,84,Fixed',
  
  // Messaggio 5: oggi 10:02 - diventa "End" alla disconnessione
  '123456789012345,2024-01-15T10:02:00.000Z,45.4660,9.1920,0,45,83,Fixed'
];

async function testTcpConnection() {
  console.log('🔌 Connecting to TCP server on port 8090...');
  
  const client = new net.Socket();
  let messageIndex = 0;
  
  client.connect(8090, 'localhost', () => {
    console.log('✅ Connected to server');
    console.log('📤 Sending test messages...\n');
    
    // Invia i messaggi con un intervallo di 2 secondi
    const sendNextMessage = () => {
      if (messageIndex < testMessages.length) {
        const message = testMessages[messageIndex];
        console.log(`📨 Sending message ${messageIndex + 1}: ${message}`);
        client.write(message + '\n');
        messageIndex++;
        
        if (messageIndex < testMessages.length) {
          setTimeout(sendNextMessage, 2000); // Aspetta 2 secondi
        } else {
          // Dopo aver inviato tutti i messaggi, aspetta un po' e disconnetti
          setTimeout(() => {
            console.log('🔌 Disconnecting from server...');
            client.end();
          }, 3000);
        }
      }
    };
    
    // Inizia a inviare i messaggi
    sendNextMessage();
  });
  
  client.on('data', (data) => {
    console.log('📥 Received from server:', data.toString());
  });
  
  client.on('close', () => {
    console.log('🔌 Connection closed');
    console.log('\n🎯 Test completed!');
    console.log('Check the server logs to see the trip creation process.');
    console.log('Expected results:');
    console.log('- 2 trips created (1 main + 1 retroactive)');
    console.log('- All 5 messages assigned to appropriate trips');
    console.log('- No orphan messages');
  });
  
  client.on('error', (err) => {
    console.error('❌ Connection error:', err);
  });
}

// Esegui il test
testTcpConnection();
