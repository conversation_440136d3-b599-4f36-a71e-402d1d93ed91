import { type User, type InsertUser, type SatelliteMessage, type InsertSatelliteMessage, type Trip } from "@shared/schema";

export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Satellite message management
  addSatelliteMessage(message: InsertSatelliteMessage): Promise<SatelliteMessage>;
  getSatelliteMessages(imeis: string[], startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]>;
  getSatelliteMessagesByImei(imei: string, startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]>;
  
  // Trip management
  getTrips(imeis: string[]): Promise<Trip[]>;
  getTripMessages(tripId: string): Promise<SatelliteMessage[]>;
  reorganizeTripAssignments(imei?: string): Promise<void>;
  
  // Device status tracking
  getOnlineDevices(): Promise<string[]>;
  setDeviceOnline(imei: string): Promise<void>;
  setDeviceOffline(imei: string): Promise<void>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private userIdCounter: number;
  private messages: Map<number, SatelliteMessage>;
  private messageIdCounter: number;
  private onlineDevices: Set<string>;

  constructor() {
    this.users = new Map();
    this.userIdCounter = 1;
    this.messages = new Map();
    this.messageIdCounter = 1;
    this.onlineDevices = new Set();
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const user: User = { ...insertUser, id, role: insertUser.role || 'user' };
    this.users.set(id, user);
    return user;
  }

  async addSatelliteMessage(insertMessage: InsertSatelliteMessage): Promise<SatelliteMessage> {
    const id = this.messageIdCounter++;

    console.log(`\n=== PROCESSING MESSAGE ===`);
    console.log(`Message ID: ${id}`);
    console.log(`IMEI: ${insertMessage.imei}`);
    console.log(`Status: ${insertMessage.status}`);
    console.log(`Raw timestamp: ${insertMessage.satelliteTimestamp}`);

    const messageTime = typeof insertMessage.satelliteTimestamp === 'string'
      ? new Date(insertMessage.satelliteTimestamp)
      : insertMessage.satelliteTimestamp;

    console.log(`Parsed timestamp: ${messageTime.toISOString()}`);

    let tripId: string | null = null;

    if (insertMessage.status === 'Start') {
      // Start messages always create a new trip (TCP connection based)
      tripId = `trip_${insertMessage.imei}_${Date.now()}`;
      console.log(`🚀 TCP CONNECTION START: Created new tripId: ${tripId}`);
      console.log(`Trip start time: ${messageTime.toISOString()}`);
    } else if (insertMessage.status === 'End') {
      // End messages close the most recent open trip
      tripId = this.findMostRecentOpenTrip(insertMessage.imei);
      if (!tripId) {
        console.log(`⚠️  WARNING: End message without open trip for IMEI ${insertMessage.imei}`);
        console.log(`This should not happen in normal TCP-based operation`);
      } else {
        console.log(`🏁 TCP CONNECTION END: Closing trip ${tripId}`);
        console.log(`Trip end time: ${messageTime.toISOString()}`);
      }
    } else {
      // Fixed/No Fix messages: find chronologically correct trip or create retroactive trip
      console.log(`📍 POSITION MESSAGE: Finding/creating appropriate trip`);
      tripId = await this.findOrCreateChronologicalTrip(insertMessage.imei, messageTime);
      console.log(`✅ Position message assigned to trip: ${tripId}`);
    }

    const message: SatelliteMessage = {
      ...insertMessage,
      id,
      serverTimestamp: new Date(),
      tripId,
    };
    this.messages.set(id, message);

    console.log(`💾 SAVED: Message ${id} → Trip ${tripId} (${insertMessage.status}) at ${messageTime.toISOString()}`);
    console.log(`Total messages in storage: ${this.messages.size}`);
    console.log(`=== END PROCESSING ===\n`);

    return message;
  }

  /**
   * Trova il viaggio aperto più recente per un IMEI (per messaggi End)
   */
  private findMostRecentOpenTrip(imei: string): string | null {
    const messages = Array.from(this.messages.values())
      .filter(m => m.imei === imei && m.status === 'Start' && m.tripId)
      .sort((a, b) => new Date(b.satelliteTimestamp).getTime() - new Date(a.satelliteTimestamp).getTime());

    console.log(`Found ${messages.length} Start messages for IMEI ${imei}`);

    // Trova il primo Start senza End corrispondente
    for (const startMsg of messages) {
      const hasEnd = Array.from(this.messages.values())
        .some(m => m.imei === imei && m.status === 'End' && m.tripId === startMsg.tripId);

      if (!hasEnd) {
        console.log(`Found open trip: ${startMsg.tripId}`);
        return startMsg.tripId;
      }
    }

    console.log(`No open trip found for IMEI ${imei}`);
    return null;
  }

  /**
   * Trova il viaggio cronologicamente corretto o crea un viaggio retroattivo
   */
  private async findOrCreateChronologicalTrip(imei: string, messageTime: Date): string | null {
    console.log(`🔍 Searching for chronologically correct trip for IMEI ${imei} at ${messageTime.toISOString()}`);

    // Prima prova a trovare un viaggio esistente che contenga cronologicamente il messaggio
    const existingTripId = this.findChronologicallyCorrectTrip(imei, messageTime);

    if (existingTripId) {
      console.log(`✅ Message fits in existing trip: ${existingTripId}`);
      return existingTripId;
    }

    // Se non trova un viaggio esistente, crea un viaggio retroattivo
    console.log(`🔄 No existing trip found - creating retroactive trip for message at ${messageTime.toISOString()}`);
    const retroactiveTripId = this.createRetroactiveTrip(imei, messageTime);
    console.log(`🆕 Created retroactive trip: ${retroactiveTripId}`);
    return retroactiveTripId;
  }

  /**
   * Trova il viaggio cronologicamente corretto per un messaggio (versione originale migliorata)
   */
  private findChronologicallyCorrectTrip(imei: string, messageTime: Date): string | null {
    const startMessages = Array.from(this.messages.values())
      .filter(m => m.imei === imei && m.status === 'Start' && m.tripId)
      .sort((a, b) => {
        const aTime = typeof a.satelliteTimestamp === 'string' ? new Date(a.satelliteTimestamp) : a.satelliteTimestamp;
        const bTime = typeof b.satelliteTimestamp === 'string' ? new Date(b.satelliteTimestamp) : b.satelliteTimestamp;
        return aTime.getTime() - bTime.getTime(); // Ordine cronologico crescente
      });

    console.log(`Checking ${startMessages.length} existing trips for IMEI ${imei}`);

    // Trova il viaggio che contiene temporalmente questo messaggio
    for (const startMsg of startMessages) {
      const endMsg = Array.from(this.messages.values())
        .find(m => m.imei === imei && m.status === 'End' && m.tripId === startMsg.tripId);

      const tripStart = typeof startMsg.satelliteTimestamp === 'string'
        ? new Date(startMsg.satelliteTimestamp)
        : startMsg.satelliteTimestamp;
      const tripEnd = endMsg
        ? (typeof endMsg.satelliteTimestamp === 'string' ? new Date(endMsg.satelliteTimestamp) : endMsg.satelliteTimestamp)
        : new Date(); // Se non c'è End, il viaggio è ancora attivo

      console.log(`Checking trip ${startMsg.tripId}: ${tripStart.toISOString()} - ${tripEnd.toISOString()}, message time: ${messageTime.toISOString()}`);

      if (messageTime >= tripStart && messageTime <= tripEnd) {
        console.log(`Message fits chronologically in trip ${startMsg.tripId}`);
        return startMsg.tripId;
      }
    }

    console.log(`No chronologically suitable trip found for message at ${messageTime.toISOString()}`);
    return null;
  }

  /**
   * Crea un viaggio retroattivo per un messaggio che non appartiene a nessun viaggio esistente
   * Ora cerca prima viaggi retroattivi vicini per estenderli invece di crearne sempre di nuovi
   */
  private createRetroactiveTrip(imei: string, messageTime: Date): string {
    console.log(`🔄 CREATING/EXTENDING RETROACTIVE TRIP for ${imei} at ${messageTime.toISOString()}`);

    // Prima cerca viaggi retroattivi esistenti che potrebbero essere estesi
    const existingRetroactiveTrip = this.findExtendableRetroactiveTrip(imei, messageTime);

    if (existingRetroactiveTrip) {
      console.log(`🔗 EXTENDING existing retroactive trip: ${existingRetroactiveTrip}`);
      return existingRetroactiveTrip;
    }

    // Se non trova un viaggio estendibile, crea un nuovo viaggio retroattivo
    const tripId = `trip_${imei}_${messageTime.getTime()}_retroactive`;

    console.log(`🆕 Creating NEW retroactive trip: ${tripId}`);
    console.log(`📅 Retroactive trip timestamp: ${messageTime.toISOString()}`);
    console.log(`📍 IMEI: ${imei}`);

    // Crea un messaggio Start fittizio per il viaggio retroattivo
    const retroactiveStartMessage: SatelliteMessage = {
      id: this.messageIdCounter++,
      imei: imei,
      satelliteTimestamp: messageTime.toISOString(),
      serverTimestamp: new Date(),
      latitude: 0, // Coordinate fittizie per il messaggio Start retroattivo
      longitude: 0,
      speed: 0,
      direction: 0,
      batteryPercentage: 0,
      status: 'Start',
      tripId: tripId
    };

    // Salva il messaggio Start retroattivo
    this.messages.set(retroactiveStartMessage.id, retroactiveStartMessage);

    console.log(`🚀 Created retroactive START message: ID ${retroactiveStartMessage.id}`);

    // Crea un messaggio End retroattivo con timestamp + 5 minuti
    // Questo dà più spazio per messaggi consecutivi
    const endTime = new Date(messageTime.getTime() + 5 * 60 * 1000); // +5 minuti
    const retroactiveEndMessage: SatelliteMessage = {
      id: this.messageIdCounter++,
      imei: imei,
      satelliteTimestamp: endTime.toISOString(),
      serverTimestamp: new Date(),
      latitude: 0,
      longitude: 0,
      speed: 0,
      direction: 0,
      batteryPercentage: 0,
      status: 'End',
      tripId: tripId
    };

    // Salva il messaggio End retroattivo
    this.messages.set(retroactiveEndMessage.id, retroactiveEndMessage);

    console.log(`🏁 Created retroactive END message: ID ${retroactiveEndMessage.id}`);
    console.log(`⏱️  Retroactive trip duration: ${messageTime.toISOString()} → ${endTime.toISOString()} (5 minutes)`);
    console.log(`✅ RETROACTIVE TRIP COMPLETED: ${tripId}`);
    console.log(`📊 Total messages now: ${this.messages.size}`);

    return tripId;
  }

  /**
   * Trova un viaggio retroattivo esistente che può essere esteso per includere il nuovo messaggio
   * Cerca viaggi retroattivi entro un range temporale ragionevole (es. 1 ora)
   */
  private findExtendableRetroactiveTrip(imei: string, messageTime: Date): string | null {
    const timeWindow = 60 * 60 * 1000; // 1 ora in millisecondi

    console.log(`🔍 Searching for extendable retroactive trips within 1 hour of ${messageTime.toISOString()}`);

    // Trova tutti i viaggi retroattivi per questo IMEI
    const retroactiveTrips = Array.from(this.messages.values())
      .filter(m => m.imei === imei && m.status === 'Start' && m.tripId?.includes('_retroactive'))
      .map(startMsg => {
        const endMsg = Array.from(this.messages.values())
          .find(m => m.imei === imei && m.status === 'End' && m.tripId === startMsg.tripId);

        return {
          tripId: startMsg.tripId,
          startTime: typeof startMsg.satelliteTimestamp === 'string'
            ? new Date(startMsg.satelliteTimestamp)
            : startMsg.satelliteTimestamp,
          endTime: endMsg
            ? (typeof endMsg.satelliteTimestamp === 'string' ? new Date(endMsg.satelliteTimestamp) : endMsg.satelliteTimestamp)
            : new Date(),
          startMsg,
          endMsg
        };
      });

    console.log(`Found ${retroactiveTrips.length} existing retroactive trips for IMEI ${imei}`);

    // Cerca un viaggio che può essere esteso
    for (const trip of retroactiveTrips) {
      const timeDiffStart = Math.abs(messageTime.getTime() - trip.startTime.getTime());
      const timeDiffEnd = Math.abs(messageTime.getTime() - trip.endTime.getTime());

      console.log(`Checking trip ${trip.tripId}: start diff ${timeDiffStart}ms, end diff ${timeDiffEnd}ms`);

      // Se il messaggio è entro 1 ora dal viaggio esistente, estendi il viaggio
      if (timeDiffStart <= timeWindow || timeDiffEnd <= timeWindow) {
        console.log(`✅ Found extendable trip: ${trip.tripId}`);

        // Estendi il viaggio aggiornando il messaggio End se necessario
        if (trip.endMsg && messageTime > trip.endTime) {
          const newEndTime = new Date(messageTime.getTime() + 5 * 60 * 1000); // +5 minuti dal nuovo messaggio
          trip.endMsg.satelliteTimestamp = newEndTime.toISOString();
          console.log(`🔄 Extended trip end time to: ${newEndTime.toISOString()}`);
        }

        return trip.tripId;
      }
    }

    console.log(`❌ No extendable retroactive trip found within time window`);
    return null;
  }

  /**
   * Riorganizza i messaggi esistenti per assegnare correttamente i tripId
   * Utile per correggere dati già importati
   */
  async reorganizeTripAssignments(imei?: string): Promise<void> {
    console.log(`Starting trip reorganization for ${imei || 'all IMEIs'}`);

    // Prima rimuovi tutti i viaggi retroattivi esistenti per evitare duplicati
    const retroactiveMessages = Array.from(this.messages.values())
      .filter(m => (!imei || m.imei === imei) && m.tripId?.includes('_retroactive'));

    console.log(`Removing ${retroactiveMessages.length} existing retroactive messages`);
    for (const msg of retroactiveMessages) {
      this.messages.delete(msg.id);
    }

    const messagesToProcess = Array.from(this.messages.values())
      .filter(m => !imei || m.imei === imei)
      .sort((a, b) => {
        const aTime = typeof a.satelliteTimestamp === 'string' ? new Date(a.satelliteTimestamp) : a.satelliteTimestamp;
        const bTime = typeof b.satelliteTimestamp === 'string' ? new Date(b.satelliteTimestamp) : b.satelliteTimestamp;
        return aTime.getTime() - bTime.getTime();
      });

    console.log(`Reorganizing ${messagesToProcess.length} messages for ${imei || 'all IMEIs'}`);

    // Rimuovi tutti i tripId dai messaggi Fixed/No Fix per riassegnarli
    for (const message of messagesToProcess) {
      if (message.status === 'Fixed' || message.status === 'No Fix') {
        message.tripId = null;
      }
    }

    // Riassegna i tripId cronologicamente usando la nuova logica
    for (const message of messagesToProcess) {
      if (message.status === 'Fixed' || message.status === 'No Fix') {
        const messageTime = typeof message.satelliteTimestamp === 'string'
          ? new Date(message.satelliteTimestamp)
          : message.satelliteTimestamp;

        // Usa la nuova logica che crea viaggi retroattivi se necessario
        const correctTripId = await this.findOrCreateChronologicalTrip(message.imei, messageTime);
        message.tripId = correctTripId;

        console.log(`Reassigned message ${message.id} to trip ${correctTripId}`);
      }
    }

    console.log('Trip reorganization completed with new retroactive trip logic');
  }

  async getSatelliteMessages(imeis: string[], startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]> {
    const messages = Array.from(this.messages.values());
    console.log('[getSatelliteMessages] startDate:', startDate ? startDate.toISOString() : null, 'endDate:', endDate ? endDate.toISOString() : null);
    const filtered = messages.filter(message => {
      if (!imeis.includes(message.imei)) return false;

      // Conversione robusta
      if (typeof message.satelliteTimestamp === 'string') {
        message.satelliteTimestamp = new Date(message.satelliteTimestamp);
      }

      // Log confronto per ogni messaggio
      console.log('[getSatelliteMessages] IMEI:', message.imei, 'satelliteTimestamp:', message.satelliteTimestamp instanceof Date ? message.satelliteTimestamp.toISOString() : message.satelliteTimestamp, 'startDate:', startDate ? startDate.toISOString() : null, 'endDate:', endDate ? endDate.toISOString() : null);

      if (startDate && message.satelliteTimestamp < startDate) return false;
      if (endDate && message.satelliteTimestamp > endDate) return false;

      return true;
    }).sort((a, b) => {
      // Conversione robusta anche qui
      const aTime = typeof a.satelliteTimestamp === 'string' ? new Date(a.satelliteTimestamp) : a.satelliteTimestamp;
      const bTime = typeof b.satelliteTimestamp === 'string' ? new Date(b.satelliteTimestamp) : b.satelliteTimestamp;
      return bTime.getTime() - aTime.getTime();
    });
    console.log(`[getSatelliteMessages] Messaggi restituiti al frontend: ${filtered.length}`);
    return filtered;
  }

  async getSatelliteMessagesByImei(imei: string, startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]> {
    return this.getSatelliteMessages([imei], startDate, endDate);
  }

  async getOnlineDevices(): Promise<string[]> {
    return Array.from(this.onlineDevices);
  }

  async setDeviceOnline(imei: string): Promise<void> {
    this.onlineDevices.add(imei);
  }

  async setDeviceOffline(imei: string): Promise<void> {
    this.onlineDevices.delete(imei);
  }

  async getTrips(imeis: string[]): Promise<Trip[]> {
    const messages = Array.from(this.messages.values());
    console.log(`Total messages in storage: ${messages.length}`);
    console.log(`Looking for trips for IMEIs: ${imeis.join(', ')}`);
    
    const startMessages = messages.filter(m => 
      imeis.includes(m.imei) && m.status === 'Start' && m.tripId
    );
    
    console.log(`Found ${startMessages.length} Start messages with tripId`);

    const trips: Trip[] = [];
    
    for (const startMsg of startMessages) {
      if (!startMsg.tripId) continue;
      
      const endMsg = messages.find(m => 
        m.imei === startMsg.imei && 
        m.status === 'End' && 
        m.tripId === startMsg.tripId
      );

      console.log(`Trip ${startMsg.tripId}: Start at ${startMsg.satelliteTimestamp}, End: ${endMsg ? endMsg.satelliteTimestamp : 'Not found'}`);

      trips.push({
        id: startMsg.tripId,
        imei: startMsg.imei,
        startTime: startMsg.satelliteTimestamp,
        endTime: endMsg ? endMsg.satelliteTimestamp : null,
        startMessage: startMsg,
        endMessage: endMsg,
      });
    }

    console.log(`Returning ${trips.length} trips`);
    return trips.sort((a, b) => 
      new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    );
  }

  async getTripMessages(tripId: string): Promise<SatelliteMessage[]> {
    const messages = Array.from(this.messages.values());
    return messages
      .filter(m => m.tripId === tripId)
      .sort((a, b) => 
        new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime()
      );
  }


}

export const storage = new MemStorage();

